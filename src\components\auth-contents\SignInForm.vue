<!-- SignInForm.vue -->
<template>
  <form @submit.prevent="handleSubmit">
    <div class="form-header">
      <h3>Welcome Back</h3>
      <p>Sign in to your account</p>
    </div>

    <div class="form-group">
      <label for="signin-email">Email</label>
      <input
        type="email"
        id="signin-email"
        v-model="email"
        placeholder="<EMAIL>"
        @blur="validateEmail"
        :class="{ invalid: emailError }"
        :disabled="disabled"
        required
      />
      <span v-if="emailError" class="error">{{ emailError }}</span>
    </div>

    <div class="form-group">
      <span class="password-field">
        <label for="signin-password">Password</label>
        <RouterLink :to="{ name: 'reset-password' }"
          >forgot password?</RouterLink
        >
      </span>
      <input
        type="password"
        id="signin-password"
        v-model="password"
        placeholder="Password"
        @blur="validatePassword"
        :class="{ invalid: passwordError }"
        :disabled="disabled"
        required
      />
      <span v-if="passwordError" class="error">{{ passwordError }}</span>
    </div>

    <button type="submit" :disabled="disabled" :class="{ disabled: disabled }">
      Sign In
    </button>
  </form>
</template>

<script>
import { ref } from "vue";

export default {
  name: "SignInForm",
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["submit-signin"],
  setup(_, { emit }) {
    const email = ref("");
    const password = ref("");

    const emailError = ref(null);
    const passwordError = ref(null);

    const validateEmail = () => {
      emailError.value = !email.value.includes("@")
        ? "Invalid email address"
        : null;
    };

    const validatePassword = () => {
      passwordError.value =
        password.value.length < 6
          ? "Password must be at least 6 characters"
          : null;
    };

    const handleSubmit = () => {
      validateEmail();
      validatePassword();

      if (!emailError.value && !passwordError.value) {
        emit("submit-signin", { email: email.value, password: password.value });
      }
    };

    return {
      email,
      password,
      emailError,
      passwordError,
      validateEmail,
      validatePassword,
      handleSubmit,
    };
  },
};
</script>

<style scoped>
@import "./styles/auth.module.css";

.invalid {
  border-color: var(--alert-error-border) !important;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1) !important;
}

.error {
  color: var(--alert-error-text);
  font-size: 0.8rem;
  margin-top: 4px;
  font-weight: 500;
  transition: color var(--transition-medium);
}

.password-field {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.password-field a {
  font-size: 12px;
  text-decoration: underline;
  color: var(--text-muted);
  transition: color var(--transition-medium);
}

.password-field a:hover {
  color: var(--primary-color);
}
</style>
