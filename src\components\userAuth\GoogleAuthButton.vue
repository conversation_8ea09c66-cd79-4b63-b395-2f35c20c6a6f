<!-- GoogleAuthButton.vue -->
<template>
  <button
    @click="handleGoogleAuth"
    class="google-auth-btn"
    :disabled="disabled"
    :class="{ disabled: disabled }"
  >
    <img src="@/assets/images/google.png" alt="Google Icon" />
    {{ buttonText }}
  </button>
</template>

<script>
export default {
  name: "GoogleAuthButton",
  props: {
    buttonText: {
      type: String,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["google-auth"],
  setup(_, { emit }) {
    const handleGoogleAuth = () => {
      emit("google-auth");
    };
    return { handleGoogleAuth };
  },
};
</script>

<style scoped>
.google-auth-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 10px;
  margin-bottom: 20px;
  background-color: #fff;
  color: #757575;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.google-auth-btn:hover {
  background-color: #f1f1f1;
}

.google-auth-btn img {
  width: 18px;
  height: 18px;
  margin-right: 10px;
}

.google-auth-btn.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: #f5f5f5;
}

.google-auth-btn.disabled:hover {
  background-color: #f5f5f5;
}
</style>
