<template>
  <div class="auth-container">
    <!-- Auth Tab buttons -->
    <AuthTabs :activeTab="activeTab" @update-tab="updateActiveTab" />

    <!-- Error message display -->
    <div v-if="error" class="error-message">
      {{ error }}
    </div>

    <!-- Loading state -->
    <div v-if="loading" class="loading-message">Authenticating...</div>

    <!-- Google Auth Button component -->
    <GoogleAuthButton
      :buttonText="
        activeTab === 'signin' ? 'Sign in with Google' : 'Sign up with Google'
      "
      @google-auth="handleGoogleAuth"
      :disabled="loading"
    />
    <!-- Google Auth Button component end -->
    <div class="auth-content">
      <transition name="slide" mode="out-in">
        <!-- Use SignInForm or SignUpForm based on the active tab -->
        <SignInForm
          v-if="activeTab === 'signin'"
          @submit-signin="handleSignIn"
          :disabled="loading"
        />
        <SignUpForm v-else @submit-signup="handleSignUp" :disabled="loading" />
      </transition>
    </div>
  </div>
</template>

<script>
import { ref } from "vue";
import AuthTabs from "@/components/userAuth/AuthTabs.vue";
import GoogleAuthButton from "@/components/userAuth/GoogleAuthButton.vue";
import SignInForm from "@/components/auth-contents/SignInForm.vue";
import SignUpForm from "@/components/auth-contents/SignUpForm.vue";
import { useRouter } from "vue-router";
import { useAuth } from "@/composables/useAuth.js";

export default {
  name: "AuthUser",
  components: {
    AuthTabs,
    GoogleAuthButton,
    SignInForm,
    SignUpForm,
  },
  setup() {
    const activeTab = ref("signin");
    const router = useRouter();
    const {
      loginWithEmail,
      signUpWithEmail,
      loginWithGoogle,
      loading,
      error,
      clearError,
    } = useAuth();

    const handleSignIn = async (formData) => {
      const result = await loginWithEmail(formData.email, formData.password);
      if (result.success) {
        router.push("/");
      }
    };

    const handleSignUp = async (formData) => {
      const result = await signUpWithEmail(
        formData.email,
        formData.password,
        formData.name
      );
      if (result.success) {
        router.push("/");
      }
    };

    const handleGoogleAuth = async () => {
      const result = await loginWithGoogle();
      if (result.success) {
        router.push("/");
      }
    };

    const updateActiveTab = (tab) => {
      activeTab.value = tab;
      clearError(); // Clear any existing errors when switching tabs
    };

    return {
      activeTab,
      handleSignIn,
      handleSignUp,
      updateActiveTab,
      handleGoogleAuth,
      loading,
      error,
    };
  },
};
</script>

<style scoped>
.auth-container {
  max-width: 400px;
  margin: 20px auto;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}

.slide-enter-from {
  transform: translateX(5%);
  opacity: 0;
}

.slide-leave-to {
  transform: translateX(-5%);
  opacity: 0;
}

.slide-enter-to,
.slide-leave-from {
  transform: translateX(0);
  opacity: 1;
}

.error-message {
  background-color: #fee;
  color: #c33;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  border: 1px solid #fcc;
  font-size: 14px;
  text-align: center;
}

.loading-message {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  border: 1px solid #bbdefb;
  font-size: 14px;
  text-align: center;
  font-weight: 500;
}
</style>
