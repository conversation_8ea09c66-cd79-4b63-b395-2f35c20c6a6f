<template>
  <div class="user-dropdown" ref="dropdownRef">
    <!-- User Avatar/Button -->
    <button
      @click="toggleDropdown"
      class="user-button"
      :class="{ active: isOpen }"
    >
      <img
        v-if="user?.photoURL"
        :src="user.photoURL"
        :alt="user.displayName || user.email"
        class="user-avatar"
      />
      <div v-else class="user-avatar-placeholder">
        {{ getUserInitials(user) }}
      </div>
      <span class="user-name">{{ getUserDisplayName(user) }}</span>
      <svg
        class="dropdown-arrow"
        :class="{ rotated: isOpen }"
        width="12"
        height="12"
        viewBox="0 0 12 12"
      >
        <path
          d="M2 4l4 4 4-4"
          stroke="currentColor"
          stroke-width="2"
          fill="none"
        />
      </svg>
    </button>

    <!-- Dropdown Menu -->
    <transition name="dropdown">
      <div v-if="isOpen" class="dropdown-menu">
        <div class="dropdown-header">
          <div class="user-info">
            <div class="user-display-name">{{ getUserDisplayName(user) }}</div>
            <div class="user-email">{{ user?.email }}</div>
          </div>
        </div>

        <div class="dropdown-divider"></div>

        <div class="dropdown-items">
          <button @click="handleProfile" class="dropdown-item">
            <svg width="16" height="16" viewBox="0 0 16 16" class="item-icon">
              <path
                d="M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0zm4 8c0 1-1 1-1 1H3s-1 0-1-1 1-4 6-4 6 3 6 4zm-1-.004c-.001-.246-.154-.986-.832-1.664C11.516 10.68 10.289 10 8 10c-2.29 0-3.516.68-4.168 1.332-.678.678-.83 1.418-.832 1.664h10z"
                fill="currentColor"
              />
            </svg>
            Profile
          </button>

          <button @click="handleSettings" class="dropdown-item">
            <IconSetting />
            Settings
          </button>
        </div>

        <div class="dropdown-divider"></div>

        <button @click="handleSignOut" class="dropdown-item sign-out">
          <svg width="16" height="16" viewBox="0 0 16 16" class="item-icon">
            <path
              d="M10 12.5a.5.5 0 0 1-.5.5h-8a.5.5 0 0 1-.5-.5v-9a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 .5.5v2a.5.5 0 0 0 1 0v-2A1.5 1.5 0 0 0 9.5 2h-8A1.5 1.5 0 0 0 0 3.5v9A1.5 1.5 0 0 0 1.5 14h8a1.5 1.5 0 0 0 1.5-1.5v-2a.5.5 0 0 0-1 0v2z"
              fill="currentColor"
            />
            <path
              d="M15.854 8.354a.5.5 0 0 0 0-.708l-3-3a.5.5 0 0 0-.708.708L14.293 7.5H5.5a.5.5 0 0 0 0 1h8.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3z"
              fill="currentColor"
            />
          </svg>
          Sign Out
        </button>
      </div>
    </transition>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from "vue";
import { useAuth } from "@/composables/useAuth.js";
import { useRouter } from "vue-router";
import IconSetting from "@/components/icons/IconSetting.vue";

export default {
  name: "UserDropdown",
  props: {
    user: {
      type: Object,
      required: true,
    },
  },
  components: {
    IconSetting,
  },
  setup() {
    const isOpen = ref(false);
    const dropdownRef = ref(null);
    const { logout } = useAuth();
    const router = useRouter();

    const toggleDropdown = () => {
      isOpen.value = !isOpen.value;
    };

    const closeDropdown = () => {
      isOpen.value = false;
    };

    const getUserDisplayName = (user) => {
      return user?.displayName || user?.email?.split("@")[0] || "User";
    };

    const getUserInitials = (user) => {
      const name = getUserDisplayName(user);
      return name.charAt(0).toUpperCase();
    };

    const handleProfile = () => {
      closeDropdown();
      // Navigate to profile page (implement when available)
      console.log("Navigate to profile");
    };

    const handleSettings = () => {
      closeDropdown();
      // Navigate to settings page (implement when available)
      console.log("Navigate to settings");
    };

    const handleSignOut = async () => {
      closeDropdown();
      const result = await logout();
      if (result.success) {
        router.push("/");
      }
    };

    // Handle click outside to close dropdown
    const handleClickOutside = (event) => {
      if (dropdownRef.value && !dropdownRef.value.contains(event.target)) {
        closeDropdown();
      }
    };

    onMounted(() => {
      document.addEventListener("click", handleClickOutside);
    });

    onUnmounted(() => {
      document.removeEventListener("click", handleClickOutside);
    });

    return {
      isOpen,
      dropdownRef,
      toggleDropdown,
      closeDropdown,
      getUserDisplayName,
      getUserInitials,
      handleProfile,
      handleSettings,
      handleSignOut,
    };
  },
};
</script>

<style scoped>
.user-dropdown {
  position: relative;
  display: inline-block;
}

.user-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: transparent;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-color);
  font-size: 14px;
}

.user-button:hover,
.user-button.active {
  background-color: var(--bg-color-light);
  border-color: rgba(0, 0, 0, 0.2);
}

.user-avatar {
  width: 25px;
  height: 25%;
  border-radius: 50%;
  object-fit: cover;
}

.user-avatar-placeholder {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background-color: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.user-name {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 12px;
}

.dropdown-arrow {
  transition: transform 0.2s ease;
  color: #666;
}

.dropdown-arrow.rotated {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 4px;
  min-width: 240px;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
}

.dropdown-header {
  padding: 16px;
  background-color: #f8f9fa;
}

.user-info {
  text-align: left;
}

.user-display-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.user-email {
  font-size: 12px;
  color: #666;
}

.dropdown-divider {
  height: 1px;
  background-color: rgba(0, 0, 0, 0.1);
}

.dropdown-items {
  padding: 8px 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px 16px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: #333;
  font-size: 14px;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-item.sign-out {
  color: #dc3545;
}

.dropdown-item.sign-out:hover {
  background-color: #fff5f5;
}

.item-icon {
  flex-shrink: 0;
  opacity: 0.7;
}

/* Dropdown transition */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.2s ease;
  transform-origin: top right;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(-10px);
}

.dropdown-enter-to,
.dropdown-leave-from {
  opacity: 1;
  transform: scale(1) translateY(0);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .dropdown-menu {
    background: #2d3748;
    border-color: #4a5568;
  }

  .dropdown-header {
    background-color: #1a202c;
  }

  .user-display-name {
    color: #e2e8f0;
  }

  .user-email {
    color: #a0aec0;
  }

  .dropdown-item {
    color: #e2e8f0;
  }

  .dropdown-item:hover {
    background-color: #4a5568;
  }

  .dropdown-item.sign-out:hover {
    background-color: #742a2a;
  }
}
</style>
