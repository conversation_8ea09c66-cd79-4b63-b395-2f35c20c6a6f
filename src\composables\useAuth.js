import { ref, onMounted } from "vue";
import {
  signInWithPopup,
  signOut,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  onAuthStateChanged,
  updateProfile,
} from "firebase/auth";
import { auth, googleProvider } from "@/firebase/config.js";

export function useAuth() {
  const isAuthenticated = ref(false);
  const user = ref(null);
  const loading = ref(true);
  const error = ref(null);

  // Use the auth and provider from the config

  // Clear error function
  const clearError = () => {
    error.value = null;
  };

  // Set loading state
  const setLoading = (state) => {
    loading.value = state;
  };

  // Handle authentication errors
  const handleAuthError = (authError) => {
    console.error("Authentication error:", authError);

    switch (authError.code) {
      case "auth/user-not-found":
        error.value = "No account found with this email address.";
        break;
      case "auth/wrong-password":
        error.value = "Incorrect password. Please try again.";
        break;
      case "auth/email-already-in-use":
        error.value = "An account with this email already exists.";
        break;
      case "auth/weak-password":
        error.value = "Password should be at least 6 characters long.";
        break;
      case "auth/invalid-email":
        error.value = "Please enter a valid email address.";
        break;
      case "auth/too-many-requests":
        error.value = "Too many failed attempts. Please try again later.";
        break;
      case "auth/popup-closed-by-user":
        error.value = "Sign-in was cancelled. Please try again.";
        break;
      default:
        error.value =
          authError.message || "An error occurred during authentication.";
    }
  };

  // Google sign-in
  const loginWithGoogle = async () => {
    try {
      clearError();
      setLoading(true);
      const result = await signInWithPopup(auth, googleProvider);
      user.value = result.user;
      isAuthenticated.value = true;
      return { success: true, user: result.user };
    } catch (authError) {
      handleAuthError(authError);
      return { success: false, error: error.value };
    } finally {
      setLoading(false);
    }
  };

  // Email/password sign-in
  const loginWithEmail = async (email, password) => {
    try {
      clearError();
      setLoading(true);
      const result = await signInWithEmailAndPassword(auth, email, password);
      user.value = result.user;
      isAuthenticated.value = true;
      return { success: true, user: result.user };
    } catch (authError) {
      handleAuthError(authError);
      return { success: false, error: error.value };
    } finally {
      setLoading(false);
    }
  };

  // Email/password sign-up
  const signUpWithEmail = async (email, password, displayName = "") => {
    try {
      clearError();
      setLoading(true);
      const result = await createUserWithEmailAndPassword(
        auth,
        email,
        password
      );

      // Update user profile with display name if provided
      if (displayName && result.user) {
        await updateProfile(result.user, { displayName });
      }

      user.value = result.user;
      isAuthenticated.value = true;
      return { success: true, user: result.user };
    } catch (authError) {
      handleAuthError(authError);
      return { success: false, error: error.value };
    } finally {
      setLoading(false);
    }
  };

  // Sign out
  const logout = async () => {
    try {
      clearError();
      setLoading(true);
      await signOut(auth);
      user.value = null;
      isAuthenticated.value = false;
      return { success: true };
    } catch (authError) {
      handleAuthError(authError);
      return { success: false, error: error.value };
    } finally {
      setLoading(false);
    }
  };

  // Initialize authentication state
  let unsubscribe = null;

  const initializeAuth = () => {
    if (unsubscribe) return; // Prevent multiple initializations

    unsubscribe = onAuthStateChanged(auth, (firebaseUser) => {
      if (firebaseUser) {
        user.value = firebaseUser;
        isAuthenticated.value = true;
      } else {
        user.value = null;
        isAuthenticated.value = false;
      }
      loading.value = false;
    });
  };

  // Initialize auth state on mount
  onMounted(() => {
    initializeAuth();
  });

  return {
    // State
    isAuthenticated,
    user,
    loading,
    error,

    // Methods
    loginWithGoogle,
    loginWithEmail,
    signUpWithEmail,
    logout,
    clearError,
    initializeAuth,

    // Legacy method for backward compatibility
    login: loginWithGoogle,
  };
}
