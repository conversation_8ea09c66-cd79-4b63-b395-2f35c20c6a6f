<template>
  <Container>
    <header class="section-heading">
      <div class="heading-text">
        <h2 class="section-title">Most Search Recipes of the Week</h2>
        <p class="muted">
          Here are lists of popular and most search recipes of the week
        </p>
      </div>
      <RouterLink :to="{ name: 'Recipes' }" class="see-all-link"
        >see all</RouterLink
      >
    </header>
    <div v-if="isLoading" class="loading-container">
      <p class="loading-text">Loading delicious recipes...</p>
    </div>
    <div v-else-if="error" class="error-container">
      <p class="error-text">{{ error }}</p>
    </div>
    <div v-else class="recipe-grid">
      <div
        class="recipe-card"
        v-for="recipe in sliceRecipes"
        :key="recipe.id"
        @click="openModal(recipe)"
        @keydown.enter="openModal(recipe)"
        @keydown.space.prevent="openModal(recipe)"
        tabindex="0"
        role="button"
        :aria-label="`View details for ${recipe.title}`"
      >
        <img
          :src="recipe.image || placeholderImage"
          :alt="`${recipe.title} recipe`"
          loading="lazy"
          @error="handleImageError"
          class="recipe-image"
        />
        <div class="recipe-card-overlay">
          <h3 class="recipe-card-title">{{ recipe.title }}</h3>
        </div>
      </div>
    </div>
    <!-- Recipe Section Modal  -->
    <RecipeModal
      :recipe="selectedRecipe"
      :showModal="isModalOpen"
      @close="closeModal"
    />
  </Container>
</template>
<script>
import { computed, ref } from "vue";
import Container from "./Container.vue";
import RecipeModal from "./RecipeModal.vue";
import useFetchRecipes from "@/composables/useFetchRecipes";
export default {
  name: "RecipeSection",
  components: {
    Container,
    RecipeModal,
  },
  setup() {
    const { recipes, fetchRecipes, error, isLoading } = useFetchRecipes();

    // Fetch recipes on component mount
    fetchRecipes("breakfast");

    const sliceRecipes = computed(() => {
      return recipes.value.slice(0, 9);
    });

    const placeholderImage = computed(() => {
      return "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='250' viewBox='0 0 300 250'%3E%3Crect width='300' height='250' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial, sans-serif' font-size='16' fill='%23999' text-anchor='middle' dy='.3em'%3ERecipe Image%3C/text%3E%3C/svg%3E";
    });

    const selectedRecipe = ref(null);
    const isModalOpen = ref(false);

    const openModal = (recipe) => {
      selectedRecipe.value = recipe;
      isModalOpen.value = true;
    };

    const closeModal = () => {
      isModalOpen.value = false;
    };

    const handleImageError = (event) => {
      // Use the same placeholder image
      event.target.src = placeholderImage.value;
      event.target.alt = "Recipe image not available";
    };

    return {
      sliceRecipes,
      openModal,
      closeModal,
      selectedRecipe,
      isModalOpen,
      handleImageError,
      error,
      isLoading,
      placeholderImage,
    };
  },
};
</script>
<style scoped>
.section-heading {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.heading-text h2 {
  font-size: 1.6rem;
  text-transform: capitalize;
  color: var(--text-color);
  font-family: var(--font-family);
  font-weight: 500;
  margin-bottom: 0.5rem;
  transition: color var(--transition-medium);
}

.section-title {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--text-color);
  transition: color var(--transition-medium);
}

.muted {
  color: var(--text-muted);
  font-size: 0.9rem;
  transition: color var(--transition-medium);
}

.see-all-link {
  text-decoration: none;
  color: var(--primary-color);
  font-weight: 600;
  padding: 8px 16px;
  border-radius: var(--border-radius);
  transition: all var(--transition-medium);
  border: 1px solid transparent;
}

.see-all-link:hover {
  color: var(--primary-color-light);
  background-color: var(--card-bg);
  border-color: var(--primary-color);
  transform: translateY(-1px);
}
.recipe-card {
  position: relative;
  cursor: pointer;
  overflow: hidden;
  border-radius: var(--border-radius);
  transition: transform var(--transition-medium),
    box-shadow var(--transition-medium);
  outline: none;
}

.recipe-card:hover,
.recipe-card:focus {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.recipe-image {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform var(--transition-medium);
}

.recipe-card:hover .recipe-image {
  transform: scale(1.05);
}

.recipe-card .recipe-card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.recipe-card:hover .recipe-card-overlay {
  opacity: 1;
}

.recipe-card .recipe-card-overlay .recipe-card-title {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--primary-color);
  font-size: 1.2rem;
  font-weight: 600;
  text-transform: capitalize;
}

.recipe-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.loading-container,
.error-container {
  text-align: center;
  padding: 2rem;
  margin: 2rem 0;
}

.loading-text {
  color: var(--text-muted);
  font-size: 1.1rem;
  animation: pulse 2s infinite;
}

.error-text {
  color: var(--error-color, #e74c3c);
  font-size: 1rem;
  background-color: var(--error-bg, rgba(231, 76, 60, 0.1));
  padding: 1rem;
  border-radius: var(--border-radius);
  border: 1px solid var(--error-color, #e74c3c);
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
